C:\Users\<USER>\.nuget\packages\freesql\3.5.211\lib\netstandard2.1\FreeSql.dll
C:\Users\<USER>\.nuget\packages\freesql.provider.sqlite\3.5.211\lib\netstandard2.0\FreeSql.Provider.Sqlite.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration\9.0.7\lib\net8.0\Microsoft.Extensions.Configuration.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.abstractions\9.0.7\lib\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.fileextensions\9.0.7\lib\net8.0\Microsoft.Extensions.Configuration.FileExtensions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.json\9.0.7\lib\net8.0\Microsoft.Extensions.Configuration.Json.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection\9.0.7\lib\net8.0\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection.abstractions\9.0.7\lib\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.diagnostics.abstractions\9.0.0\lib\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.abstractions\9.0.7\lib\net8.0\Microsoft.Extensions.FileProviders.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.physical\9.0.7\lib\net8.0\Microsoft.Extensions.FileProviders.Physical.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.filesystemglobbing\9.0.7\lib\net8.0\Microsoft.Extensions.FileSystemGlobbing.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.hosting.abstractions\9.0.0\lib\net8.0\Microsoft.Extensions.Hosting.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging\9.0.0\lib\net8.0\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\9.0.0\lib\net8.0\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.options\9.0.0\lib\net8.0\Microsoft.Extensions.Options.dll
C:\Users\<USER>\.nuget\packages\microsoft.extensions.primitives\9.0.7\lib\net8.0\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\.nuget\packages\serilog\4.3.0\lib\net8.0\Serilog.dll
C:\Users\<USER>\.nuget\packages\serilog.extensions.hosting\9.0.0\lib\net8.0\Serilog.Extensions.Hosting.dll
C:\Users\<USER>\.nuget\packages\serilog.extensions.logging\9.0.0\lib\net8.0\Serilog.Extensions.Logging.dll
C:\Users\<USER>\.nuget\packages\serilog.sinks.console\6.0.0\lib\net8.0\Serilog.Sinks.Console.dll
C:\Users\<USER>\.nuget\packages\serilog.sinks.file\7.0.0\lib\net8.0\Serilog.Sinks.File.dll
C:\Users\<USER>\.nuget\packages\stub.system.data.sqlite.core.netstandard\1.0.119\lib\netstandard2.1\System.Data.SQLite.dll
C:\Users\<USER>\.nuget\packages\system.diagnostics.diagnosticsource\9.0.0\lib\net8.0\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\.nuget\packages\system.io.pipelines\9.0.7\lib\net8.0\System.IO.Pipelines.dll
C:\Users\<USER>\.nuget\packages\system.text.encodings.web\9.0.7\lib\net8.0\System.Text.Encodings.Web.dll
C:\Users\<USER>\.nuget\packages\system.text.json\9.0.7\lib\net8.0\System.Text.Json.dll
