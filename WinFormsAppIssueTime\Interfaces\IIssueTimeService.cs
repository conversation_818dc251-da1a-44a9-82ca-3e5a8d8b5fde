﻿using WinFormsAppIssueTime.Models;

namespace WinFormsAppIssueTime.Interfaces;

/// <summary>
/// 时间发放服务接口
/// 提供发放时间的创建、查询、删除等功能
/// </summary>
public interface IIssueTimeService
{
    /// <summary>
    /// 创建发放记录
    /// 生成一年的发放时间数据并保存到数据库
    /// </summary>
    Task<IssueTime> CreateIssueTimeAsync(DateTime issueTimeYear);

    /// <summary>
    /// 获取当前时间对应的发放时间段，如果当前时间不在任何时间段内，则返回最近的未来时间段
    /// </summary>
    /// <returns>当前或下一个发放时间段，如果没有找到则返回null</returns>
    Task<IssueTime?> GetCurrentOrNextIssueTimeAsync();

    /// <summary>
    /// 清除缓存
    /// 强制下次查询时重新从数据库获取数据
    /// </summary>
    void ClearCache();
}